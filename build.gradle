plugins {
    id 'java'
    id 'application'
}

group = 'com.example'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}

repositories {
    mavenCentral()
}

dependencies {
    // GLM SDK
    implementation 'ai.z.openapi:zai-sdk:0.0.4'
    
    // Logging
    implementation 'org.slf4j:slf4j-simple:1.7.36'
    
    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
}

application {
    mainClass = 'com.example.glm.QuickStart'
}

// 定义多个运行任务
task runQuickStart(type: JavaExec) {
    group = 'application'
    description = 'Run QuickStart demo'
    classpath = sourceSets.main.runtimeClasspath
    mainClass = 'com.example.glm.QuickStart'
}

task runAdvanced(type: JavaExec) {
    group = 'application'
    description = 'Run AdvancedDemo'
    classpath = sourceSets.main.runtimeClasspath
    mainClass = 'com.example.glm.AdvancedDemo'
}

task runConfigurable(type: JavaExec) {
    group = 'application'
    description = 'Run ConfigurableDemo'
    classpath = sourceSets.main.runtimeClasspath
    mainClass = 'com.example.glm.ConfigurableDemo'
}

// 编译时的编码设置
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

// 测试配置
test {
    useJUnit()
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
}
