package com.example.glm;

import ai.z.openapi.ZhipuAiClient;
import ai.z.openapi.service.model.*;
import java.util.Arrays;
import java.util.Scanner;

/**
 * 可配置的GLM-4.5演示
 * 支持选择不同的预设配置
 */
public class ConfigurableDemo {
    
    private final ZhipuAiClient client;
    private GLMConfig.Preset currentPreset;
    
    public ConfigurableDemo(String apiKey) {
        this.client = ZhipuAiClient.builder()
            .apiKey(apiKey)
            .build();
        this.currentPreset = GLMConfig.BALANCED_CHAT; // 默认配置
    }
    
    public static void main(String[] args) {
        String apiKey = GLMConfig.getApiKeyFromEnv();
        
        if (!GLMConfig.isValidApiKey(apiKey)) {
            System.err.println("错误: 请设置有效的环境变量 GLM_API_KEY");
            System.err.println("或者在代码中直接设置API Key");
            return;
        }
        
        ConfigurableDemo demo = new ConfigurableDemo(apiKey);
        demo.runInteractiveSession();
    }
    
    /**
     * 运行交互式会话
     */
    public void runInteractiveSession() {
        System.out.println("=== GLM-4.5 可配置演示 ===");
        showCurrentConfig();
        showCommands();
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("\n> ");
            String input = scanner.nextLine().trim();
            
            if (input.equalsIgnoreCase("quit") || input.equalsIgnoreCase("exit")) {
                System.out.println("再见！");
                break;
            }
            
            if (input.equalsIgnoreCase("help")) {
                showCommands();
                continue;
            }
            
            if (input.equalsIgnoreCase("config")) {
                showCurrentConfig();
                continue;
            }
            
            if (input.equalsIgnoreCase("presets")) {
                showPresets();
                continue;
            }
            
            if (input.toLowerCase().startsWith("preset ")) {
                String presetName = input.substring(7);
                changePreset(presetName);
                continue;
            }
            
            if (input.toLowerCase().startsWith("test ")) {
                String testType = input.substring(5);
                runTest(testType);
                continue;
            }
            
            if (!input.isEmpty()) {
                callGLM(input);
            }
        }
        
        scanner.close();
    }
    
    /**
     * 显示当前配置
     */
    private void showCurrentConfig() {
        System.out.println("\n=== 当前配置 ===");
        System.out.println("模型: " + currentPreset.getModel());
        System.out.println("温度: " + currentPreset.getTemperature());
        System.out.println("最大Token: " + currentPreset.getMaxTokens());
        System.out.println("描述: " + currentPreset.getDescription());
    }
    
    /**
     * 显示命令帮助
     */
    private void showCommands() {
        System.out.println("\n=== 可用命令 ===");
        System.out.println("help - 显示此帮助信息");
        System.out.println("config - 显示当前配置");
        System.out.println("presets - 显示所有预设配置");
        System.out.println("preset <名称> - 切换预设配置");
        System.out.println("test <类型> - 运行测试 (creative/precise/code)");
        System.out.println("quit/exit - 退出程序");
        System.out.println("直接输入消息 - 与GLM-4.5对话");
    }
    
    /**
     * 显示所有预设配置
     */
    private void showPresets() {
        System.out.println("\n=== 可用预设配置 ===");
        GLMConfig.Preset[] presets = GLMConfig.getAllPresets();
        for (int i = 0; i < presets.length; i++) {
            GLMConfig.Preset preset = presets[i];
            System.out.printf("%d. %s (温度: %.1f, Token: %d)\n   %s\n", 
                i + 1, getPresetName(preset), preset.getTemperature(), 
                preset.getMaxTokens(), preset.getDescription());
        }
        System.out.println("\n使用 'preset <名称>' 切换配置，如: preset creative");
    }
    
    /**
     * 获取预设配置的名称
     */
    private String getPresetName(GLMConfig.Preset preset) {
        if (preset == GLMConfig.CREATIVE_WRITING) return "creative";
        if (preset == GLMConfig.BALANCED_CHAT) return "balanced";
        if (preset == GLMConfig.PRECISE_QA) return "precise";
        if (preset == GLMConfig.CODE_GENERATION) return "code";
        if (preset == GLMConfig.LONG_CONTENT) return "long";
        return "unknown";
    }
    
    /**
     * 切换预设配置
     */
    private void changePreset(String presetName) {
        GLMConfig.Preset newPreset = GLMConfig.getPresetByName(presetName);
        this.currentPreset = newPreset;
        System.out.println("已切换到预设: " + newPreset.getDescription());
        showCurrentConfig();
    }
    
    /**
     * 运行测试
     */
    private void runTest(String testType) {
        String testMessage;
        GLMConfig.Preset originalPreset = currentPreset;
        
        switch (testType.toLowerCase()) {
            case "creative":
                currentPreset = GLMConfig.CREATIVE_WRITING;
                testMessage = "写一首关于春天的诗";
                break;
            case "precise":
                currentPreset = GLMConfig.PRECISE_QA;
                testMessage = "什么是机器学习？请给出准确的定义。";
                break;
            case "code":
                currentPreset = GLMConfig.CODE_GENERATION;
                testMessage = "用Java写一个计算斐波那契数列的方法";
                break;
            default:
                System.out.println("未知的测试类型。可用类型: creative, precise, code");
                return;
        }
        
        System.out.println("运行测试: " + testType);
        System.out.println("使用配置: " + currentPreset.getDescription());
        System.out.println("测试问题: " + testMessage);
        System.out.println();
        
        callGLM(testMessage);
        
        // 恢复原配置
        currentPreset = originalPreset;
        System.out.println("\n已恢复原配置");
    }
    
    /**
     * 调用GLM模型
     */
    private void callGLM(String userMessage) {
        try {
            System.out.println("正在调用GLM-4.5...");
            
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                .model(currentPreset.getModel())
                .messages(Arrays.asList(
                    ChatMessage.builder()
                        .role(ChatMessageRole.USER.value())
                        .content(userMessage)
                        .build()
                ))
                .stream(false)
                .temperature(currentPreset.getTemperature())
                .maxTokens(currentPreset.getMaxTokens())
                .build();

            ChatCompletionResponse response = client.chat().createChatCompletion(request);

            if (response.getData() != null && 
                response.getData().getChoices() != null && 
                !response.getData().getChoices().isEmpty()) {
                
                String assistantMessage = response.getData().getChoices().get(0).getMessage().getContent();
                System.out.println("\nGLM-4.5 回复:");
                System.out.println(assistantMessage);
                
                // 显示使用的token信息（如果可用）
                if (response.getData().getUsage() != null) {
                    System.out.println("\nToken使用情况:");
                    System.out.println("输入Token: " + response.getData().getUsage().getPromptTokens());
                    System.out.println("输出Token: " + response.getData().getUsage().getCompletionTokens());
                    System.out.println("总Token: " + response.getData().getUsage().getTotalTokens());
                }
                
            } else {
                System.out.println("抱歉，没有收到有效的回复。");
            }
            
        } catch (Exception e) {
            System.err.println("调用GLM-4.5时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
