<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="4" failures="0" name="com.example.glm.GLMConfigTest" time="0.016" errors="0" skipped="0">
  <properties>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="java.vm.version" value="********+1-LTS-2"/>
    <property name="sun.boot.library.path" value="D:\Java\jdk-********\bin"/>
    <property name="maven.multiModuleProjectDirectory" value="E:\work\kiro\myidea\.kiro\specs\glm-demo"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="path.separator" value=";"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="E:\work\kiro\myidea\.kiro\specs\glm-demo"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="java.runtime.version" value="********+1-LTS-2"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="os.name" value="Windows 10"/>
    <property name="classworlds.conf" value="D:\Servers\apache-maven-3.6.1\bin\..\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Java\jdk-********\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;D:\Servers\Oracle11gR2\product\11.2.0\dbhome_1\bin;D:\Servers\apache-maven-3.6.1\bin;D:\Java\jdk-********\bin;D:\Java\jdk-********\jre\bin;C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\iCLS\;C:\Program Files\Intel\Intel(R) Management Engine Components\iCLS\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\IPT;C:\Program Files\Intel\Intel(R) Management Engine Components\IPT;D:\Servers\androidsdk\tools;D:\Servers\androidsdk\platform-tools;D:\Servers\androidsdk\build-tools;D:\Servers\MongoDB\Server\4.0\bin;D:\Servers\Redis-x64-3.2.100;D:\Servers\mysql-5.7.25-winx64\bin;D:\Servers\php-7.3.4-x64;D:\Servers\php-7.3.4-x64\ext;D:\Programs\Tesseract-OCR\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;D:\Servers\apache-tomcat-9.0.33\bin;D:\Programs\Git\cmd;D:\Programs\TortoiseGit\bin;D:\Servers\gradle-7.1.1\bin;D:\Program Files\OpenVPN\bin;D:\Servers\apache-maven-3.6.1\bin;D:\Program Files\TortoiseSVN\bin;D:\Servers\apache-ant-1.7.0\bin;D:\Servers\androidsdk\tools\bin;D:\Servers\androidsdk\emulator;D:\Servers\nvm;D:\Servers\nodejs;D:\Program Files (x86)\Tencent\微信web开发者工具\dll;D:\Program Files\Go\bin;D:\Program Files (x86)\Kiro\bin;;C:\Program Files\Docker\Docker\resources\bin;D:\Servers\Python26;C:\Programs\Python\Python36\;C:\Programs\Python\Python36\Scripts\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\IDM Computer Solutions\UltraEdit\;D:\Programs\JetBrains\IntelliJ IDEA 2018.3.2\bin;D:\Programs\Tesseract-OCR;D:\Programs\Microsoft VS Code\bin;D:\Projects\nodejs\node_global;D:\Servers\consul_1.10.1_win64;C:\Users\<USER>\AppData\Roaming\npm;D:\Servers\nvm;D:\Servers\nodejs;D:\Programs\Fiddler;D:\Program Files (x86)\Tencent\微信web开发者工具\dll;E:\Program Files\JetBrains\IntelliJ IDEA 2023.2.1\bin;;E:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\go\bin;E:\Program Files\Geth;D:\Program Files (x86)\Kiro\bin;E:\Program Files\Windsurf\bin;."/>
    <property name="maven.conf" value="D:\Servers\apache-maven-3.6.1\bin\../conf"/>
    <property name="jdk.debug" value="release"/>
    <property name="java.class.version" value="61.0"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="os.version" value="10.0"/>
    <property name="library.jansi.path" value="D:\Servers\apache-maven-3.6.1\bin\..\lib\jansi-native"/>
    <property name="user.home" value="C:\Users\<USER>\Servers\apache-maven-3.6.1\bin\..\boot\plexus-classworlds-2.6.0.jar"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="sun.java.command" value="org.codehaus.plexus.classworlds.launcher.Launcher test"/>
    <property name="java.home" value="D:\Java\jdk-********"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.version" value="********"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="sun.stderr.encoding" value="ms936"/>
    <property name="maven.home" value="D:\Servers\apache-maven-3.6.1\bin\.."/>
    <property name="file.separator" value="\"/>
    <property name="java.version.date" value="2022-08-18"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.stdout.encoding" value="ms936"/>
    <property name="sun.cpu.isalist" value="amd64"/>
  </properties>
  <testcase classname="com.example.glm.GLMConfigTest" name="testIsValidApiKey" time="0.016"/>
  <testcase classname="com.example.glm.GLMConfigTest" name="testGetAllPresets" time="0"/>
  <testcase classname="com.example.glm.GLMConfigTest" name="testGetPresetByName" time="0"/>
  <testcase classname="com.example.glm.GLMConfigTest" name="testPresetProperties" time="0"/>
</testsuite>