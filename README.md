# GLM-4.5 大模型调用演示

这是一个使用Java调用GLM-4.5大模型的完整演示项目，包含多种使用场景和配置选项。

## 功能特性

- ✅ 基础的GLM-4.5模型调用
- ✅ 多轮对话支持
- ✅ 流式响应演示
- ✅ 多种预设配置（创意写作、精确问答、代码生成等）
- ✅ 交互式命令行界面
- ✅ 完整的错误处理
- ✅ 单元测试

## 项目结构

```
src/
├── main/java/com/example/glm/
│   ├── QuickStart.java          # 基础演示
│   ├── AdvancedDemo.java        # 高级功能演示（多轮对话）
│   ├── ConfigurableDemo.java    # 可配置演示
│   └── GLMConfig.java           # 配置管理类
└── test/java/com/example/glm/
    └── GLMConfigTest.java       # 单元测试
```

## 快速开始

### 1. 环境要求

- Java 11 或更高版本
- Maven 3.6 或更高版本
- GLM API Key

### 2. 获取API Key

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并获取API Key
3. 设置环境变量：
   ```bash
   # Windows
   set GLM_API_KEY=your_api_key_here
   
   # Linux/Mac
   export GLM_API_KEY=your_api_key_here
   ```

### 3. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行基础演示
mvn exec:java -Dexec.mainClass="com.example.glm.QuickStart"

# 运行高级演示
mvn exec:java -Dexec.mainClass="com.example.glm.AdvancedDemo"

# 运行可配置演示
mvn exec:java -Dexec.mainClass="com.example.glm.ConfigurableDemo"
```

#### 使用启动脚本

```bash
# Windows
run.bat

# Linux/Mac
chmod +x run.sh
./run.sh
```

### 4. 运行测试

```bash
mvn test
```

## 使用示例

### 基础调用

```java
// 初始化客户端
ZhipuAiClient client = ZhipuAiClient.builder()
    .apiKey("YOUR_API_KEY")
    .build();

// 创建请求
ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
    .model("glm-4.5")
    .messages(Arrays.asList(
        ChatMessage.builder()
            .role(ChatMessageRole.USER.value())
            .content("Hello, who are you?")
            .build()
    ))
    .stream(false)
    .temperature(0.6f)
    .maxTokens(1024)
    .build();

// 发送请求并获取响应
ChatCompletionResponse response = client.chat().createChatCompletion(request);
String reply = response.getData().getChoices().get(0).getMessage().getContent();
```

### 使用预设配置

```java
// 使用创意写作配置
GLMConfig.Preset preset = GLMConfig.CREATIVE_WRITING;

ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
    .model(preset.getModel())
    .temperature(preset.getTemperature())
    .maxTokens(preset.getMaxTokens())
    // ... 其他参数
    .build();
```

## 预设配置说明

| 配置名称 | 温度值 | Token限制 | 适用场景 |
|---------|--------|-----------|----------|
| CREATIVE_WRITING | 0.9 | 2048 | 创意写作，需要高创造性 |
| BALANCED_CHAT | 0.7 | 1024 | 日常对话，平衡创造性和准确性 |
| PRECISE_QA | 0.3 | 1024 | 精确问答，需要高准确性 |
| CODE_GENERATION | 0.2 | 2048 | 代码生成，需要高逻辑性 |
| LONG_CONTENT | 0.6 | 4096 | 长内容生成 |

## 交互式命令

### ConfigurableDemo 支持的命令：

- `help` - 显示帮助信息
- `config` - 显示当前配置
- `presets` - 显示所有预设配置
- `preset <名称>` - 切换预设配置
- `test <类型>` - 运行测试（creative/precise/code）
- `quit/exit` - 退出程序

### AdvancedDemo 支持的命令：

- `clear` - 清空对话历史
- `history` - 查看对话历史
- `stream <消息>` - 使用流式响应
- `normal <消息>` - 使用普通响应

## 错误处理

项目包含完整的错误处理机制：

1. **API Key验证** - 检查API Key格式和有效性
2. **网络错误处理** - 处理网络连接问题
3. **响应验证** - 验证API响应的完整性
4. **用户输入验证** - 验证用户输入的有效性

## 依赖说明

- `ai.z.openapi:zai-sdk:0.0.4` - GLM官方SDK
- `org.slf4j:slf4j-simple` - 日志框架
- `com.fasterxml.jackson.core:jackson-databind` - JSON处理
- `junit:junit` - 单元测试框架

## 注意事项

1. **API Key安全** - 不要在代码中硬编码API Key，使用环境变量
2. **Token限制** - 注意不同模型的token限制
3. **频率限制** - 遵守API的调用频率限制
4. **错误重试** - 在生产环境中实现适当的重试机制

## 常见问题

### Q: 如何设置API Key？
A: 设置环境变量 `GLM_API_KEY`，或在代码中直接替换 `YOUR_API_KEY`。

### Q: 支持哪些模型？
A: 目前支持 `glm-4.5`、`glm-4`、`glm-3-turbo` 等模型。

### Q: 如何实现流式响应？
A: 在请求中设置 `stream(true)`，具体实现可能需要根据SDK版本调整。

### Q: 如何处理长文本？
A: 使用 `LONG_CONTENT` 预设配置，或手动设置更大的 `maxTokens` 值。

## 许可证

本项目仅用于演示目的，请遵守GLM API的使用条款。
