package com.example.glm;

import ai.z.openapi.ZhipuAiClient;
import ai.z.openapi.service.model.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * GLM-4.5 高级功能演示
 * 包括多轮对话、流式响应等功能
 */
public class AdvancedDemo {
    
    private static final String DEFAULT_MODEL = "glm-4.5";
    private static final float DEFAULT_TEMPERATURE = 0.7f;
    private static final int DEFAULT_MAX_TOKENS = 2048;
    
    private final ZhipuAiClient client;
    private final List<ChatMessage> conversationHistory;
    
    public AdvancedDemo(String apiKey) {
        this.client = ZhipuAiClient.builder()
            .apiKey(apiKey)
            .build();
        this.conversationHistory = new ArrayList<>();
    }
    
    public static void main(String[] args) {
        String apiKey = System.getenv("GLM_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            apiKey = "YOUR_API_KEY";
            System.out.println("警告: 请设置环境变量 GLM_API_KEY");
        }
        
        AdvancedDemo demo = new AdvancedDemo(apiKey);
        demo.runInteractiveSession();
    }
    
    /**
     * 运行交互式会话
     */
    public void runInteractiveSession() {
        System.out.println("=== GLM-4.5 高级演示 (支持多轮对话) ===");
        System.out.println("命令:");
        System.out.println("  quit/exit - 退出程序");
        System.out.println("  clear - 清空对话历史");
        System.out.println("  history - 查看对话历史");
        System.out.println("  stream <消息> - 使用流式响应");
        System.out.println("  normal <消息> - 使用普通响应");
        System.out.println("  直接输入消息 - 使用普通响应");
        System.out.println();
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("您: ");
            String input = scanner.nextLine().trim();
            
            if (input.equalsIgnoreCase("quit") || input.equalsIgnoreCase("exit")) {
                System.out.println("再见！");
                break;
            }
            
            if (input.equalsIgnoreCase("clear")) {
                conversationHistory.clear();
                System.out.println("对话历史已清空。");
                continue;
            }
            
            if (input.equalsIgnoreCase("history")) {
                showHistory();
                continue;
            }
            
            if (input.toLowerCase().startsWith("stream ")) {
                String message = input.substring(7);
                if (!message.isEmpty()) {
                    handleStreamResponse(message);
                }
                continue;
            }
            
            if (input.toLowerCase().startsWith("normal ")) {
                String message = input.substring(7);
                if (!message.isEmpty()) {
                    handleNormalResponse(message);
                }
                continue;
            }
            
            if (!input.isEmpty()) {
                handleNormalResponse(input);
            }
        }
        
        scanner.close();
    }
    
    /**
     * 处理普通响应
     */
    private void handleNormalResponse(String userMessage) {
        try {
            // 添加用户消息到历史
            conversationHistory.add(ChatMessage.builder()
                .role(ChatMessageRole.USER.value())
                .content(userMessage)
                .build());
            
            // 创建请求
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                .model(DEFAULT_MODEL)
                .messages(new ArrayList<>(conversationHistory))
                .stream(false)
                .temperature(DEFAULT_TEMPERATURE)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .build();
            
            // 发送请求
            ChatCompletionResponse response = client.chat().createChatCompletion(request);
            
            if (response.getData() != null && 
                response.getData().getChoices() != null && 
                !response.getData().getChoices().isEmpty()) {
                
                Object content = response.getData().getChoices().get(0).getMessage().getContent();
                String assistantMessage = content != null ? content.toString() : "抱歉，没有收到有效的回复。";
                System.out.println("GLM-4.5: " + assistantMessage);
                
                // 添加助手回复到历史
                conversationHistory.add(ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT.value())
                    .content(assistantMessage)
                    .build());
            } else {
                System.out.println("GLM-4.5: 抱歉，没有收到有效的回复。");
            }
            
        } catch (Exception e) {
            System.err.println("调用GLM-4.5时发生错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 处理流式响应
     */
    private void handleStreamResponse(String userMessage) {
        try {
            // 添加用户消息到历史
            conversationHistory.add(ChatMessage.builder()
                .role(ChatMessageRole.USER.value())
                .content(userMessage)
                .build());
            
            System.out.print("GLM-4.5 (流式): ");
            
            // 注意：流式响应的具体实现可能需要根据SDK的实际API进行调整
            // 这里提供一个基本的框架
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                .model(DEFAULT_MODEL)
                .messages(new ArrayList<>(conversationHistory))
                .stream(true)
                .temperature(DEFAULT_TEMPERATURE)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .build();
            
            // 对于流式响应，可能需要使用不同的方法
            // 这里先使用普通响应作为示例
            ChatCompletionResponse response = client.chat().createChatCompletion(request);
            
            if (response.getData() != null && 
                response.getData().getChoices() != null && 
                !response.getData().getChoices().isEmpty()) {
                
                Object content = response.getData().getChoices().get(0).getMessage().getContent();
                String assistantMessage = content != null ? content.toString() : "抱歉，没有收到有效的回复。";
                
                // 模拟流式输出效果
                for (char c : assistantMessage.toCharArray()) {
                    System.out.print(c);
                    try {
                        Thread.sleep(20); // 添加小延迟模拟流式效果
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                System.out.println();
                
                // 添加助手回复到历史
                conversationHistory.add(ChatMessage.builder()
                    .role(ChatMessageRole.ASSISTANT.value())
                    .content(assistantMessage)
                    .build());
            }
            
        } catch (Exception e) {
            System.err.println("流式调用GLM-4.5时发生错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 显示对话历史
     */
    private void showHistory() {
        System.out.println("\n=== 对话历史 ===");
        if (conversationHistory.isEmpty()) {
            System.out.println("暂无对话历史");
        } else {
            for (int i = 0; i < conversationHistory.size(); i++) {
                ChatMessage msg = conversationHistory.get(i);
                String role = msg.getRole().equals(ChatMessageRole.USER.value()) ? "您" : "GLM-4.5";
                System.out.println((i + 1) + ". " + role + ": " + msg.getContent());
            }
        }
        System.out.println();
    }
}
