package com.example.glm;

import ai.z.openapi.ZhipuAiClient;
import ai.z.openapi.service.model.*;
import java.util.Arrays;
import java.util.Scanner;

/**
 * GLM-4.5 大模型调用演示
 * 
 * 使用前请设置环境变量 GLM_API_KEY 或直接在代码中替换 YOUR_API_KEY
 */
public class QuickStart {
    
    private static final String DEFAULT_MODEL = "glm-4.5";
    private static final float DEFAULT_TEMPERATURE = 0.6f;
    private static final int DEFAULT_MAX_TOKENS = 1024;
    
    public static void main(String[] args) {
        // 从环境变量获取API Key，如果没有则使用默认值
        String apiKey = System.getenv("GLM_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            apiKey = "YOUR_API_KEY"; // 请替换为您的实际API Key
            System.out.println("警告: 请设置环境变量 GLM_API_KEY 或在代码中替换 YOUR_API_KEY");
        }
        
        // 初始化客户端
        ZhipuAiClient client = ZhipuAiClient.builder()
            .apiKey(apiKey)
            .build();
        
        System.out.println("=== GLM-4.5 大模型演示 ===");
        System.out.println("输入 'quit' 或 'exit' 退出程序");
        System.out.println("输入 'help' 查看帮助信息");
        System.out.println();
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("请输入您的问题: ");
            String userInput = scanner.nextLine().trim();
            
            if (userInput.equalsIgnoreCase("quit") || userInput.equalsIgnoreCase("exit")) {
                System.out.println("再见！");
                break;
            }
            
            if (userInput.equalsIgnoreCase("help")) {
                showHelp();
                continue;
            }
            
            if (userInput.isEmpty()) {
                System.out.println("请输入有效的问题。");
                continue;
            }
            
            try {
                // 调用GLM-4.5模型
                String response = callGLM(client, userInput);
                System.out.println("\nGLM-4.5 回复:");
                System.out.println(response);
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("调用GLM-4.5时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }
    
    /**
     * 调用GLM-4.5模型
     */
    private static String callGLM(ZhipuAiClient client, String userMessage) {
        // 创建聊天完成请求
        ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
            .model(DEFAULT_MODEL)
            .messages(Arrays.asList(
                ChatMessage.builder()
                    .role(ChatMessageRole.USER.value())
                    .content(userMessage)
                    .build()
            ))
            .stream(false)
            .temperature(DEFAULT_TEMPERATURE)
            .maxTokens(DEFAULT_MAX_TOKENS)
            .build();

        // 发送请求
        ChatCompletionResponse response = client.chat().createChatCompletion(request);

        // 获取回复
        if (response.getData() != null && 
            response.getData().getChoices() != null && 
            !response.getData().getChoices().isEmpty()) {
            
            Object content = response.getData().getChoices().get(0).getMessage().getContent();
            return content != null ? content.toString() : "抱歉，没有收到有效的回复。";
        } else {
            return "抱歉，没有收到有效的回复。";
        }
    }
    
    /**
     * 显示帮助信息
     */
    private static void showHelp() {
        System.out.println("\n=== 帮助信息 ===");
        System.out.println("1. 直接输入问题，GLM-4.5会为您回答");
        System.out.println("2. 输入 'quit' 或 'exit' 退出程序");
        System.out.println("3. 输入 'help' 查看此帮助信息");
        System.out.println("4. 确保已设置环境变量 GLM_API_KEY");
        System.out.println();
    }
}
