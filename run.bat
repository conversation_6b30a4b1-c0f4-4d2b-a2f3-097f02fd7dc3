@echo off
echo GLM-4.5 Demo 启动脚本
echo.

REM 检查是否设置了API Key
if "%GLM_API_KEY%"=="" (
    echo 警告: 未设置环境变量 GLM_API_KEY
    echo 请先设置API Key: set GLM_API_KEY=your_api_key_here
    echo.
)

echo 选择要运行的演示:
echo 1. 基础演示 (QuickStart)
echo 2. 高级演示 (AdvancedDemo) - 支持多轮对话
echo 3. 可配置演示 (ConfigurableDemo) - 支持预设配置
echo 4. 运行测试
echo 5. 编译项目
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动基础演示...
    mvn exec:java -Dexec.mainClass="com.example.glm.QuickStart"
) else if "%choice%"=="2" (
    echo 启动高级演示...
    mvn exec:java -Dexec.mainClass="com.example.glm.AdvancedDemo"
) else if "%choice%"=="3" (
    echo 启动可配置演示...
    mvn exec:java -Dexec.mainClass="com.example.glm.ConfigurableDemo"
) else if "%choice%"=="4" (
    echo 运行测试...
    mvn test
) else if "%choice%"=="5" (
    echo 编译项目...
    mvn clean compile
) else (
    echo 无效选择
)

echo.
pause
