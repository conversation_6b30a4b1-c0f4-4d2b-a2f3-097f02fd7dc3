package com.example.glm;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * GLMConfig类的单元测试
 */
public class GLMConfigTest {
    
    @Test
    public void testGetPresetByName() {
        // 测试获取预设配置
        GLMConfig.Preset creative = GLMConfig.getPresetByName("creative");
        assertEquals(GLMConfig.MODEL_GLM_4_5, creative.getModel());
        assertEquals(0.9f, creative.getTemperature(), 0.01f);
        
        GLMConfig.Preset balanced = GLMConfig.getPresetByName("balanced");
        assertEquals(GLMConfig.MODEL_GLM_4_5, balanced.getModel());
        assertEquals(0.7f, balanced.getTemperature(), 0.01f);
        
        GLMConfig.Preset precise = GLMConfig.getPresetByName("precise");
        assertEquals(GLMConfig.MODEL_GLM_4_5, precise.getModel());
        assertEquals(0.3f, precise.getTemperature(), 0.01f);
        
        GLMConfig.Preset code = GLMConfig.getPresetByName("code");
        assertEquals(GLMConfig.MODEL_GLM_4_5, code.getModel());
        assertEquals(0.2f, code.getTemperature(), 0.01f);
        
        // 测试未知名称返回默认配置
        GLMConfig.Preset unknown = GLMConfig.getPresetByName("unknown");
        assertEquals(GLMConfig.BALANCED_CHAT, unknown);
    }
    
    @Test
    public void testIsValidApiKey() {
        // 测试有效的API Key
        assertTrue(GLMConfig.isValidApiKey("valid_api_key_12345"));
        assertTrue(GLMConfig.isValidApiKey("sk-1234567890abcdef"));
        
        // 测试无效的API Key
        assertFalse(GLMConfig.isValidApiKey(null));
        assertFalse(GLMConfig.isValidApiKey(""));
        assertFalse(GLMConfig.isValidApiKey("YOUR_API_KEY"));
        assertFalse(GLMConfig.isValidApiKey("short"));
    }
    
    @Test
    public void testGetAllPresets() {
        GLMConfig.Preset[] presets = GLMConfig.getAllPresets();
        assertEquals(5, presets.length);
        
        // 验证所有预设都不为null
        for (GLMConfig.Preset preset : presets) {
            assertNotNull(preset);
            assertNotNull(preset.getModel());
            assertNotNull(preset.getDescription());
            assertTrue(preset.getTemperature() >= 0.0f);
            assertTrue(preset.getTemperature() <= 1.0f);
            assertTrue(preset.getMaxTokens() > 0);
        }
    }
    
    @Test
    public void testPresetProperties() {
        // 测试创意写作预设
        GLMConfig.Preset creative = GLMConfig.CREATIVE_WRITING;
        assertEquals(GLMConfig.MODEL_GLM_4_5, creative.getModel());
        assertEquals(0.9f, creative.getTemperature(), 0.01f);
        assertEquals(2048, creative.getMaxTokens());
        assertTrue(creative.getDescription().contains("创意"));
        
        // 测试代码生成预设
        GLMConfig.Preset code = GLMConfig.CODE_GENERATION;
        assertEquals(GLMConfig.MODEL_GLM_4_5, code.getModel());
        assertEquals(0.2f, code.getTemperature(), 0.01f);
        assertEquals(2048, code.getMaxTokens());
        assertTrue(code.getDescription().contains("代码"));
        
        // 测试精确问答预设
        GLMConfig.Preset precise = GLMConfig.PRECISE_QA;
        assertEquals(GLMConfig.MODEL_GLM_4_5, precise.getModel());
        assertEquals(0.3f, precise.getTemperature(), 0.01f);
        assertEquals(1024, precise.getMaxTokens());
        assertTrue(precise.getDescription().contains("精确"));
    }
}
