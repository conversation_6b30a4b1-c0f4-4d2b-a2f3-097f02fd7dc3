package com.example.glm;

/**
 * GLM模型配置类
 * 包含不同场景下的模型参数配置
 */
public class GLMConfig {
    
    // 模型名称
    public static final String MODEL_GLM_4_5 = "glm-4.5";
    public static final String MODEL_GLM_4 = "glm-4";
    public static final String MODEL_GLM_3_TURBO = "glm-3-turbo";
    
    // 预设配置
    public static class Preset {
        private String model;
        private float temperature;
        private int maxTokens;
        private String description;
        
        public Preset(String model, float temperature, int maxTokens, String description) {
            this.model = model;
            this.temperature = temperature;
            this.maxTokens = maxTokens;
            this.description = description;
        }
        
        // Getters
        public String getModel() { return model; }
        public float getTemperature() { return temperature; }
        public int getMaxTokens() { return maxTokens; }
        public String getDescription() { return description; }
    }
    
    // 预设配置常量
    public static final Preset CREATIVE_WRITING = new Preset(
        MODEL_GLM_4_5, 0.9f, 2048, "创意写作 - 高创造性"
    );
    
    public static final Preset BALANCED_CHAT = new Preset(
        MODEL_GLM_4_5, 0.7f, 1024, "平衡对话 - 适中创造性"
    );
    
    public static final Preset PRECISE_QA = new Preset(
        MODEL_GLM_4_5, 0.3f, 1024, "精确问答 - 低创造性，高准确性"
    );
    
    public static final Preset CODE_GENERATION = new Preset(
        MODEL_GLM_4_5, 0.2f, 2048, "代码生成 - 极低创造性，高逻辑性"
    );
    
    public static final Preset LONG_CONTENT = new Preset(
        MODEL_GLM_4_5, 0.6f, 4096, "长内容生成 - 大token限制"
    );
    
    /**
     * 获取所有预设配置
     */
    public static Preset[] getAllPresets() {
        return new Preset[] {
            CREATIVE_WRITING,
            BALANCED_CHAT,
            PRECISE_QA,
            CODE_GENERATION,
            LONG_CONTENT
        };
    }
    
    /**
     * 根据名称获取预设配置
     */
    public static Preset getPresetByName(String name) {
        switch (name.toLowerCase()) {
            case "creative":
            case "creative_writing":
                return CREATIVE_WRITING;
            case "balanced":
            case "balanced_chat":
                return BALANCED_CHAT;
            case "precise":
            case "precise_qa":
                return PRECISE_QA;
            case "code":
            case "code_generation":
                return CODE_GENERATION;
            case "long":
            case "long_content":
                return LONG_CONTENT;
            default:
                return BALANCED_CHAT; // 默认配置
        }
    }
    
    /**
     * 验证API Key格式
     */
    public static boolean isValidApiKey(String apiKey) {
        return apiKey != null && 
               !apiKey.isEmpty() && 
               !apiKey.equals("YOUR_API_KEY") &&
               apiKey.length() > 10; // 简单的长度检查
    }
    
    /**
     * 获取环境变量中的API Key
     */
    public static String getApiKeyFromEnv() {
        String apiKey = System.getenv("GLM_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            // 尝试其他可能的环境变量名
            apiKey = System.getenv("ZHIPU_API_KEY");
        }
        return apiKey;
    }
}
