#!/bin/bash

echo "GLM-4.5 Demo 启动脚本"
echo

# 检查是否设置了API Key
if [ -z "$GLM_API_KEY" ]; then
    echo "警告: 未设置环境变量 GLM_API_KEY"
    echo "请先设置API Key: export GLM_API_KEY=your_api_key_here"
    echo
fi

echo "选择要运行的演示:"
echo "1. 基础演示 (QuickStart)"
echo "2. 高级演示 (AdvancedDemo) - 支持多轮对话"
echo "3. 可配置演示 (ConfigurableDemo) - 支持预设配置"
echo "4. 运行测试"
echo "5. 编译项目"
echo

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "启动基础演示..."
        mvn exec:java -Dexec.mainClass="com.example.glm.QuickStart"
        ;;
    2)
        echo "启动高级演示..."
        mvn exec:java -Dexec.mainClass="com.example.glm.AdvancedDemo"
        ;;
    3)
        echo "启动可配置演示..."
        mvn exec:java -Dexec.mainClass="com.example.glm.ConfigurableDemo"
        ;;
    4)
        echo "运行测试..."
        mvn test
        ;;
    5)
        echo "编译项目..."
        mvn clean compile
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo
read -p "按回车键退出..."
